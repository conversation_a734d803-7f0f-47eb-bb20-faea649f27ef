/**
 * @file IMAP error handling utilities
 */

import { getGlobalProxy, getNextProxy } from '../storeService';

/**
 * Detects if a password is a Microsoft OAuth token
 */
export function isMicrosoftToken(password: string): boolean {
  // Microsoft tokens can have various patterns:
  // 1. Contains 'M.C' pattern (common)
  // 2. Very long strings (>80 chars) with specific patterns
  // 3. Contains Microsoft-specific patterns like 'EwA', 'eyJ', etc.

  if (password.includes('M.C') && password.length > 50) {
    return true;
  }

  // Check for other Microsoft token patterns
  if (password.length > 80 && (
    password.includes('EwA') ||  // Microsoft access token prefix
    password.includes('eyJ') ||  // JWT token prefix
    password.match(/^[A-Za-z0-9+/=._-]{80,}$/) // Base64-like long string
  )) {
    return true;
  }

  return false;
}

/**
 * Extracts access token from Microsoft format password
 */
export function extractMicrosoftToken(password: string): { password: string; token: string } | null {
  // Microsoft format: password:token or just token
  const parts = password.split(':');

  if (parts.length >= 2) {
    // If we have multiple parts, assume first is password, rest is token
    const actualPassword = parts[0];
    const token = parts.slice(1).join(':');

    if (isMicrosoftToken(token)) {
      return { password: actualPassword, token };
    }
  }

  // If the entire string is a token
  if (isMicrosoftToken(password)) {
    return { password: '', token: password };
  }

  return null;
}

/**
 * Creates user-friendly error messages from IMAP errors
 */
export function createUserFriendlyErrorMessage(err: Error, accountEmail: string, hostName: string): string {
  if ((err.message?.toLowerCase().includes('authentication')) === true) {
    // Special handling for Microsoft accounts
    if (accountEmail.includes('@hotmail.com') || accountEmail.includes('@outlook.com') || accountEmail.includes('@live.com')) {
      return 'Microsoft account authentication failed. This account may require OAuth2 authentication or an App Password. Please check if you\'re using the correct credentials format.';
    }
    return 'Authentication failed. Please check your email and password. If you use 2-Factor Authentication, you may need to generate an App Password.';
  } else if ((err.message?.toLowerCase().includes('timeout')) === true) {
    return `Connection to ${hostName} timed out. Please check your network and server address.`;
  } else {
    return `A connection error occurred: ${err.message}`;
  }
}

/**
 * Logs IMAP connection errors with user-friendly messages
 */
export function logImapError(err: Error, accountEmail: string, hostName: string, logCallback: (message: string, level?: 'info' | 'error' | 'success') => void): void {
  // eslint-disable-next-line no-console
  console.error(`IMAP connection error for ${accountEmail}:`, err);

  const userFriendlyMessage = createUserFriendlyErrorMessage(err, accountEmail, hostName);
  logCallback(userFriendlyMessage, 'error');
}

interface AccountConfig {
  incoming: {
    host: string;
    port: number;
    useTls: boolean;
  };
  email: string;
  password: string;
  useProxy?: boolean;
  displayName?: string;
}

interface ImapConfig {
  host: string;
  port: number;
  secure: boolean;
  auth: {
    user: string;
    pass?: string;
    accessToken?: string;
  };
  proxy?: string;
  logger: false;
  connTimeout: number;
  authTimeout: number;
}

/**
 * Checks if an email belongs to Microsoft domains
 */
function isMicrosoftDomain(email: string): boolean {
  const microsoftDomains = ['@hotmail.com', '@outlook.com', '@live.com', '@msn.com'];
  return microsoftDomains.some(domain => email.toLowerCase().includes(domain));
}

/**
 * Creates IMAP connection configuration with OAuth2 support
 */
export function createImapConfig(account: AccountConfig, proxy?: string): ImapConfig {
  // Debug logging
  console.log(`[DEBUG] createImapConfig for ${account.email}`);
  console.log(`[DEBUG] Password length: ${account.password.length}`);
  console.log(`[DEBUG] Password preview: ${account.password.substring(0, 20)}...`);
  console.log(`[DEBUG] Is Microsoft domain: ${isMicrosoftDomain(account.email)}`);

  // Check if this is a Microsoft token
  const tokenInfo = extractMicrosoftToken(account.password);
  console.log(`[DEBUG] Token info:`, tokenInfo);

  // For Microsoft domains, try OAuth2 if we have a long password that could be a token
  const isMicrosoftAccount = isMicrosoftDomain(account.email);
  const hasLongPassword = account.password.length > 50;

  if ((tokenInfo && tokenInfo.token) || (isMicrosoftAccount && hasLongPassword)) {
    const accessToken = tokenInfo?.token || account.password;
    console.log(`[DEBUG] Using OAuth2 authentication for Microsoft account`);
    console.log(`[DEBUG] Access token length: ${accessToken.length}`);

    // Use OAuth2 authentication for Microsoft tokens
    return {
      host: account.incoming.host,
      port: account.incoming.port,
      secure: account.incoming.useTls,
      auth: {
        user: account.email,
        accessToken: accessToken,
      },
      proxy,
      logger: false,
      connTimeout: 30000,
      authTimeout: 30000,
    };
  }

  console.log(`[DEBUG] Using standard password authentication`);
  // Standard password authentication
  return {
    host: account.incoming.host,
    port: account.incoming.port,
    secure: account.incoming.useTls,
    auth: {
      user: account.email,
      pass: account.password,
    },
    proxy,
    logger: false,
    connTimeout: 30000,
    authTimeout: 30000,
  };
}

/**
 * Configures proxy for IMAP connection
 */
export async function configureProxy(account: AccountConfig, logCallback: (message: string, level?: 'info' | 'error' | 'success') => void): Promise<{ proxy: string | undefined; proxyUsed: boolean }> {
  let proxy: string | undefined;
  let proxyUsed = false;

  if (account.useProxy === true) {
    // Try to get a proxy from the rotation list first
    const nextProxy = getNextProxy();

    // If there's no proxy in the rotation list, fall back to global proxy
    const proxyConfig = nextProxy ?? await getGlobalProxy();

    if (proxyConfig && proxyConfig.enabled === true && (proxyConfig.hostPort?.length ?? 0) > 0) {
      logCallback(`Connecting account '${(account.displayName?.length ?? 0) > 0 ? account.displayName : account.email}' via proxy ${proxyConfig.hostPort}`, 'info');

      const authPart = (proxyConfig.auth === true && (proxyConfig.username?.length ?? 0) > 0) ?
        `${encodeURIComponent(proxyConfig.username ?? '')}:${encodeURIComponent(proxyConfig.password ?? '')}@` : '';
      proxy = `${proxyConfig.type}://${authPart}${proxyConfig.hostPort}`;
      proxyUsed = true;
    } else {
      logCallback(`Proxy is enabled for '${(account.displayName?.length ?? 0) > 0 ? account.displayName : account.email}', but no proxy is available. Connecting directly.`, 'info');
    }
  }

  return { proxy, proxyUsed };
}
