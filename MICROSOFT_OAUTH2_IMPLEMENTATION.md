# Microsoft OAuth2 Authentication Implementation

## Overview

This document describes the implementation of Microsoft OAuth2 authentication support for the IMAP email viewer application. The implementation enables automatic detection and proper authentication of Microsoft accounts using OAuth2 tokens.

## Problem Solved

Previously, the application could parse Microsoft account credentials in the format `email|password|token`, but IMAP authentication was failing because the system was attempting to use standard password authentication with Microsoft tokens, which requires OAuth2/XOAUTH2 authentication instead.

## Implementation Details

### 1. Token Detection

**File:** `src/services/utils/imapErrorHandling.ts`

Added `isMicrosoftToken()` function that identifies Microsoft OAuth tokens by:
- Checking for the presence of 'M.C' pattern (Microsoft token signature)
- Verifying minimum token length (>50 characters)

```typescript
export function isMic<PERSON>oftToken(password: string): boolean {
  return password.includes('M.C') && password.length > 50;
}
```

### 2. Token Extraction

**File:** `src/services/utils/imapErrorHandling.ts`

Added `extractMicrosoftToken()` function that:
- Parses combined password:token format
- Separates actual password from OAuth token
- Returns structured data for authentication

```typescript
export function extractMicrosoftToken(password: string): { password: string; token: string } | null
```

### 3. IMAP Configuration Enhancement

**File:** `src/services/utils/imapErrorHandling.ts`

Modified `createImapConfig()` to:
- Detect Microsoft tokens automatically
- Use `accessToken` field for OAuth2 authentication instead of `pass`
- Maintain backward compatibility with standard password authentication

### 4. Enhanced Error Messages

**Files:** 
- `src/services/utils/imapErrorHandling.ts`
- `src/ipc/imapFlow.ts`

Added specific error messages for Microsoft account authentication failures to help users understand OAuth2 requirements.

### 5. Parsing Service Updates

**Files:**
- `src/services/accountImportService.ts`
- `src/services/clipboardService.ts`
- `src/services/instantImportService.ts`

Updated Microsoft format parsing to:
- Properly handle token-only credentials
- Use tokens directly as authentication credentials when detected
- Maintain compatibility with password:token combinations

## Supported Microsoft Account Formats

The system now supports these Microsoft credential formats:

1. **Token Only:** `M.C542_BL2.0.U.-CQwJTnKYc8VnIdYjQKdJhOGBX*2TIiN4Z8g...`
2. **Password + Token:** `password123:M.C542_BL2.0.U.-CQwJTnKYc8VnIdYjQKdJhOGBX*2TIiN4Z8g...`
3. **Full Format:** `<EMAIL>|password|M.C542_BL2.0.U.-CQwJTnKYc8VnIdYjQKdJhOGBX*2TIiN4Z8g...`

## Authentication Flow

1. **Credential Parsing:** System detects Microsoft format during import
2. **Token Detection:** `isMicrosoftToken()` identifies OAuth tokens
3. **Token Extraction:** `extractMicrosoftToken()` separates components
4. **IMAP Configuration:** `createImapConfig()` generates OAuth2 config
5. **Authentication:** ImapFlow uses `accessToken` for XOAUTH2 authentication

## Technical Benefits

- **Automatic Detection:** No manual configuration required
- **Backward Compatibility:** Standard password authentication still works
- **Error Clarity:** Specific error messages for Microsoft accounts
- **Security:** Uses proper OAuth2 authentication method
- **Reliability:** Reduces authentication failures for Microsoft accounts

## Testing

The implementation was tested with:
- Microsoft token detection accuracy
- Token extraction functionality
- IMAP configuration generation
- OAuth2 vs standard authentication selection

All tests passed successfully, confirming proper OAuth2 authentication support for Microsoft accounts.

## Files Modified

1. `src/services/utils/imapErrorHandling.ts` - Core OAuth2 logic
2. `src/services/accountImportService.ts` - File import parsing
3. `src/services/clipboardService.ts` - Clipboard parsing
4. `src/services/instantImportService.ts` - Instant import parsing
5. `src/ipc/imapFlow.ts` - Error message enhancement

## Next Steps

- Test with real Microsoft accounts to validate OAuth2 authentication
- Monitor authentication success rates for Microsoft accounts
- Consider adding support for other OAuth2 providers if needed
