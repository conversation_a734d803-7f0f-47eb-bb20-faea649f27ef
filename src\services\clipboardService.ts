/**
 * @file Service for clipboard operations and credential parsing
 */

// Browser API declarations for Electron renderer
declare const navigator: {
  clipboard: {
    writeText: (text: string) => Promise<void>;
    readText: () => Promise<string>;
  };
};

export interface ParsedCredentials {
  email: string;
  password: string;
}

export interface ClipboardParseResult {
  success: boolean;
  credentials?: ParsedCredentials;
  error?: string;
}

/**
 * Service for handling clipboard operations
 */
export class ClipboardService {
  /**
   * Attempts to read text from clipboard
   */
  static async readText(): Promise<string | null> {
    try {
      return await navigator.clipboard.readText();
    } catch (error) {
      // eslint-disable-next-line no-console
      console.log('Could not read clipboard content:', error);
      return null;
    }
  }

  /**
   * Writes text to clipboard
   */
  static async writeText(text: string): Promise<boolean> {
    try {
      await navigator.clipboard.writeText(text);
      return true;
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('Could not write to clipboard:', error);
      return false;
    }
  }

  /**
   * Microsoft format patterns for complex credential structures
   */
  private static readonly MICROSOFT_PATTERNS = [
    // Pattern: email|password|token (most common)
    /^([^@\s]+@[^@\s]+\.[^@\s]+)\|([^|]+)\|(.+)$/,
    // Pattern: email:password:token
    /^([^@\s]+@[^@\s]+\.[^@\s]+):([^:]+):(.+)$/,
    // Pattern: email;password;token
    /^([^@\s]+@[^@\s]+\.[^@\s]+);([^;]+);(.+)$/,
    // Pattern with spaces around separators
    /^([^@\s]+@[^@\s]+\.[^@\s]+)\s*\|\s*([^|]+)\s*\|\s*(.+)$/,
  ];

  /**
   * Attempts to parse Microsoft format credentials (email:password:token)
   */
  private static parseMicrosoftFormat(text: string): ClipboardParseResult {
    console.log(`[DEBUG] parseMicrosoftFormat called with text: ${text.substring(0, 50)}...`);

    for (const pattern of this.MICROSOFT_PATTERNS) {
      const match = text.match(pattern);
      if (match) {
        console.log(`[DEBUG] Microsoft pattern matched:`, match);
        const email = match[1].trim();
        const password = match[2].trim();
        const token = match[3].trim();

        console.log(`[DEBUG] Parsed - email: ${email}, password: ${password.substring(0, 10)}..., token: ${token.substring(0, 20)}...`);

        // For Microsoft format, use the token directly as it's the actual auth credential
        const authCredential = token.includes('M.C') ? token : `${password}:${token}`;
        console.log(`[DEBUG] Auth credential: ${authCredential.substring(0, 20)}...`);

        if (/^\S+@\S+\.\S+$/.test(email) && (password || token)) {
          console.log(`[DEBUG] Microsoft format parsing successful`);
          return {
            success: true,
            credentials: {
              email,
              password: authCredential
            }
          };
        }
      }
    }
    console.log(`[DEBUG] Microsoft format parsing failed`);
    return {
      success: false,
      error: 'Microsoft format detected but invalid'
    };
  }

  /**
   * Parses credentials string with common separators
   */
  static parseCredentialsString(text: string): ClipboardParseResult {
    console.log(`[DEBUG] parseCredentialsString called with: ${text.substring(0, 50)}...`);

    // First try Microsoft format parsing
    const microsoftResult = this.parseMicrosoftFormat(text);
    if (microsoftResult.success) {
      console.log(`[DEBUG] Microsoft format parsing successful`);
      return microsoftResult;
    }

    // Regex to find common separators
    const separators = /[:;|]/;

    // Check if the input value contains a separator
    if (separators.test(text)) {
      // Split the string into email and password parts
      const parts = text.split(separators);
      if (parts.length >= 2) {
        const extractedEmail = parts[0].trim();
        // Join the rest of the parts in case the separator exists in the password
        const extractedPassword = parts.slice(1).join(parts[0].match(separators)?.[0] ?? '').trim();

        // Validate email format
        if (/^\S+@\S+\.\S+$/.test(extractedEmail) && (extractedPassword?.length ?? 0) > 0) {
          return {
            success: true,
            credentials: {
              email: extractedEmail,
              password: extractedPassword
            }
          };
        }
      }
    }

    return {
      success: false,
      error: 'Invalid credentials format'
    };
  }

  /**
   * Attempts to detect and parse credentials from clipboard
   */
  static async detectCredentialsFromClipboard(): Promise<ClipboardParseResult> {
    const clipboardText = await this.readText();

    if (clipboardText === null || clipboardText === undefined || clipboardText.length === 0) {
      return {
        success: false,
        error: 'Could not read clipboard'
      };
    }

    // Use the enhanced parseCredentialsString method
    return this.parseCredentialsString(clipboardText);
  }

  /**
   * Formats account credentials for clipboard
   */
  static formatAccountCredentials(email: string, password: string): string {
    return `${email}:${password}`;
  }

  /**
   * Copies account credentials to clipboard
   */
  static async copyAccountCredentials(email: string, password: string): Promise<boolean> {
    const formatted = this.formatAccountCredentials(email, password);
    return await this.writeText(formatted);
  }
}
